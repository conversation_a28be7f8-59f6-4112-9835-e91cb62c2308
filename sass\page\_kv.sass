// //*-- index --*/
.kv
  $kv: &
  height: 980px
  position: relative
  text-align: center
  background-image: url($path + 'kv_bottom.png'), url($path + 'kv_people.png'), url($path + 'kv_bg.jpg')
  background-size: cover, 1240px auto, cover
  background-position: bottom, calc(50% - 90px) 98%, center
  background-repeat: no-repeat, no-repeat, no-repeat
  @media (max-width:$bk-mblg)
    padding-top: 50px
  &::after
    content: ''
    position: absolute
    top: 384px
    left: 50%
    transform: translateX(-285%)
    width: 145px
    height: 125px
    background-image: url($path + 'kv_people_light.png')
    background-size: 100% auto
    background-repeat: no-repeat
    background-position: center
    pointer-events: none
    animation: flash 1.5s ease infinite

  &__title
    display: block
    width: 719px
    height: auto
    position: relative
    top: 50%
    left: 50%
    transform: translate(-45%, -70%)
    // @media (max-width:$bk-tb)
    //   width: 600px
    //   max-width: 719px
    @media (max-width:$bk-sm)
      margin-top: 57px

    // &--desktop
    //   display: block
    //   @media (max-width:$bk-sm)
    //     display: none
    // &--mobile
    //   display: none
    //   @media (max-width:$bk-sm)
    //     display: block

  &__subtitle
    position: relative
    color: #3c7d5a
    font-size: $fontsize-30
    font-weight: $fontweight-bold
    background-color: #ffffff
    padding: 15px 10px 15px 30px
    border-radius: 30px
    display: inline-block
    top: 345px
    box-shadow: 0 -9px 10px rgba(0, 0, 0, 0.1)
    @media (max-width:$bk-tb)
      // top: 280px
      font-size: $fontsize-24
      padding: 10px 5px 10px 20px
      border-radius: 20px
      box-shadow: 0 -6px 8px rgba(0, 0, 0, 0.1)

    &-text
      padding-right: 20px
      &--highlight
        padding: 2px 30px
        color: #ffeeb9
        background-color: #3c7d5a
        border-radius: 30px
